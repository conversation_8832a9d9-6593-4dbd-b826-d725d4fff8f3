package com.example.thirdplatform;

import com.example.lang.response.ResponseBase;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

@FeignClient(value = "third-platform-gateway", contextId = "order-o2o-api")
public interface O2OOrderApi {

    /**
     * 订单接单
     */
    @PostMapping("/third-platform/order/{platformCode}/confirm")
    ResponseBase<Void> confirm(@PathVariable(value = "platformCode") String platformCode, @RequestBody ThirdPlatformRequest<OrderConfirmParam> request);

    /**
     * 催单回复
     */
    @PostMapping("/third-platform/order/{platformCode}/remind/reply")
    ResponseBase<Void> remindReply(@PathVariable(value = "platformCode") String platformCode, @RequestBody ThirdPlatformRequest<OrderRemindReplyParam> request);

    /**
     * O2O订单拣货完成通知平台
     */
    @PostMapping("/third-platform/order/{platformCode}/picking/end")
    ResponseBase<Void> packingEnd(@PathVariable(value = "platformCode") String platformCode, @RequestBody ThirdPlatformRequest<OrderPickingEndParam> request);

    /**
     * 运力操作
     */
    @PostMapping("/third-platform/order/{platformCode}/rider/dispatcher")
    ResponseBase<Void> riderDispatcher(@PathVariable(value = "platformCode") String platformCode, @Valid @RequestBody ThirdPlatformRequest<OrderDispatcherParam> request);

    /**
     * 自配送-上传骑手位置
     */
    @PostMapping("/third-platform/order/{platformCode}/upload/rider/location")
    ResponseBase<Void> uploadRiderLocation(@PathVariable(value = "platformCode") String platformCode, @RequestBody ThirdPlatformRequest<RiderLocationSyncParam> request);

    /**
     * 自配送-上传骑手状态
     */
    @PostMapping("/third-platform/order/{platformCode}/upload/rider/status")
    ResponseBase<Void> uploadRiderStatus(@PathVariable(value = "platformCode") String platformCode, @RequestBody ThirdPlatformRequest<RiderStatusSyncParam> request);

    /**
     * 自配送-订单发货
     */
    @PostMapping("/third-platform/order/{platformCode}/ship")
    ResponseBase<Void> orderShip(@PathVariable(value = "platformCode") String platformCode, @Valid @RequestBody ThirdPlatformRequest<OrderShipParam> request);

    /**
     * 自配送-订单妥投
     */
    @PostMapping("/third-platform/order/{platformCode}/ship/end")
    ResponseBase<Void> orderShipEnd(@PathVariable(value = "platformCode") String platformCode, @Valid @RequestBody ThirdPlatformRequest<OrderShipEndParam> request);

    /**
     * 用户自提码校验
     */
    @PostMapping("/third-platform/order/{platformCode}/pick-up/check")
    ResponseBase<Void> userPickUpCheck(@PathVariable(value = "platformCode") String platformCode, @Valid @RequestBody ThirdPlatformRequest<UserPickUpCheckParam> request);

    /**
     * 订单取消（全额退款）
     */
    @PostMapping("/third-platform/order/{platformCode}/cancel/apply")
    ResponseBase<Void> orderCancelApply(@PathVariable(value = "platformCode") String platformCode, @Valid @RequestBody ThirdPlatformRequest<OrderCancelParam> request);

    /**
     * 订单部分退款
     */
    @PostMapping("/third-platform/order/{platformCode}/part-refund/apply")
    ResponseBase<Void> orderPartRefundApply(@PathVariable(value = "platformCode") String platformCode, @Valid @RequestBody ThirdPlatformRequest<OrderPartRefundParam> request);

    /**
     * 售后审核
     */
    @PostMapping("/third-platform/order/{platformCode}/refund/audit")
    ResponseBase<Void> refundAudit(@PathVariable(value = "platformCode") String platformCode, @Valid @RequestBody ThirdPlatformRequest<RefundAuditParam> request);

    /**
     * 补单
     */
    @PostMapping("/third-platform/order/{platformCode}/repair")
    ResponseBase<Void> repairOrder(@PathVariable(value = "platformCode") String platformCode, @Valid @RequestBody ThirdPlatformRequest<OrderRepairParam> request);

    /**
     * 获取订单的处方信息
     */
    @PostMapping("/third-platform/order/{platformCode}/prescription")
    ResponseBase<List<OrderPrescriptionResult>> getOrderPrescription(@PathVariable(value = "platformCode") String platformCode, @Valid @RequestBody ThirdPlatformRequest<OrderPrescriptionParam> request);

    /**
     * 获取订单的评论信息
     */
    @PostMapping("/third-platform/order/{platformCode}/comment/query")
    ResponseBase<OrderCommentQueryResult> orderCommentQuery(@PathVariable(value = "platformCode") String platformCode, @Valid @RequestBody ThirdPlatformRequest<OrderCommentQueryParam> request);

    /**
     * 订单评论回复
     */
    @PostMapping("/third-platform/order/{platformCode}/comment/reply")
    ResponseBase<Void> orderCommentReply(@PathVariable(value = "platformCode") String platformCode, @Valid @RequestBody ThirdPlatformRequest<OrderCommentReplyParam> request);

    /**
     * 获取订单详情，返回JSON String
     */
    @PostMapping("/third-platform/order/{platformCode}/detail/query")
    ResponseBase<String> queryOrderInfo(@PathVariable(value = "platformCode") String platformCode, @Valid @RequestBody ThirdPlatformRequest<OrderQueryParam> request);

    /**
     * 获取退单详情，返回JSON String
     */
    @PostMapping("/third-platform/order/{platformCode}/refund/detail/query")
    ResponseBase<String> queryRefundInfo(@PathVariable(value = "platformCode") String platformCode, @Valid @RequestBody ThirdPlatformRequest<RefundQueryParam> request);

    /**
     * 隐私信息解密
     */
    @PostMapping("/third-platform/order/{platformCode}/batch/decrypt")
    ResponseBase<List<OrderDataBatchDecryptResult>> orderDataBatchDecrypt(@PathVariable(value = "platformCode") String platformCode, @Valid @RequestBody ThirdPlatformRequest<OrderDataBatchDecryptParam> request);

    /**
     * 根据自提码查询订单
     */
    @PostMapping("/third-platform/order/{platformCode}/query/by-self-code")
    ResponseBase<OrderDataBySelfCodeResult> orderQueryBySelfCode(@PathVariable(value = "platformCode") String platformCode, @Valid @RequestBody ThirdPlatformRequest<OrderQueryBySelfCodeParam> request);

    /**
     * 门店自提通知
     * @param platformCode
     * @param request
     * @return
     */
    @PostMapping("/third-platform/order/{platformCode}/store/self-pick/msg")
    ResponseBase<Void> storeSelfPickMsg(@PathVariable(value = "platformCode") String platformCode, @RequestBody ThirdPlatformRequest<OrderStoreSelfPickMsgParam> request);


    /**
     * <AUTHOR>
     * @Description 查询平台订单号
     * @Date 15:13 2025/2/6
     * @param platformCode
     * @param request
     * @return com.yxt.lang.dto.api.ResponseBase<java.util.List<java.lang.String>>
     **/
    @PostMapping("/third-platform/order/{platformCode}/queryOrderNoList")
    ResponseBase<List<String>> queryOrderNoList(@PathVariable(value = "platformCode") String platformCode, @Valid @RequestBody ThirdPlatformRequest<OrderListReqParam> request);

    /**
     * <AUTHOR>
     * @Description 根据订单编号查询订单状态
     * @Date 15:03 2025/2/19
     * @param platformCode
     * @param request
     * @return com.yxt.lang.dto.api.ResponseBase<com.yxt.thirdplatform.dto.response.OrderStatusResult>
     **/
    @PostMapping("/third-platform/order/{platformCode}/orderStatusQuery")
    ResponseBase<OrderStatusResult> orderStatusQuery(@PathVariable(value = "platformCode") String platformCode, @Valid @RequestBody ThirdPlatformRequest<OrderStatusQueryParam> request);


    /**
     * 处方单审核
     *
     * @param platformCode
     * @param request
     * @return com.yxt.lang.dto.api.ResponseBase<Void>
     */
    @PostMapping("/third-platform/order/{platformCode}/prescriptionAudit")
    ResponseBase<Void> prescriptionAudit(@PathVariable(value = "platformCode") String platformCode, @Valid @RequestBody ThirdPlatformRequest<PrescriptionAuditParam> request);

}
