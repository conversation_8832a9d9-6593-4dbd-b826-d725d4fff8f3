package com.yxt.thirdplatform.dto;

import com.yxt.thirdplatform.util.MD5Util;
import lombok.Data;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Data
public class ThirdPlatformRequest<T> {

    /**
     * 企业编码
     */
    private String merCode;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 网店编码
     */
    private String onlineClientCode;

    /**
     * 店铺编码
     */
    private String onlineStoreCode;

    /**
     * 平台侧的门店id
     */
    private String platformShopId;

    /**
     * 具体请求数据
     */
    @Valid
    private T body;

    /**
     * 数据签名
     */
    private String sign;

    /**
     * 请求时间：格式yyyy-MM-dd HH:mm:ss,时区为GMT+8(误差范围5分钟)
     */
    private String timestamp;


    public ThirdPlatformRequest() {
        this.timestamp = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                .format(LocalDateTime.now());
    }

    public void setBody(T body, String sessionKey) {
        this.body = body;
        // 生成签名数据
        this.sign = generateSign(sessionKey);
    }

    public String generateSign(String sessionKey) {
        StringBuilder sb = new StringBuilder();
        sb.append(sessionKey);
        sb.append("body");
        sb.append(this.body);
        sb.append("onlineClientCode");
        sb.append(this.onlineClientCode);
        sb.append("platformCode");
        sb.append(this.platformCode);
        sb.append("merCode");
        sb.append(this.merCode);
        sb.append("timestamp");
        sb.append(this.timestamp);
        sb.append(sessionKey);
        String md5 = MD5Util.MD5Encode(sb.toString(), "UTF-8");
        return md5.toUpperCase();
    }
}
